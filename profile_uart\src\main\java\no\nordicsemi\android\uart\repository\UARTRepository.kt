/**
 * UART仓库类
 *
 * 负责管理UART服务的所有数据和业务逻辑：
 * 1. 数据管理：
 *    - 设备连接状态
 *    - 电池电量
 *    - 消息记录
 *    - 配置信息
 *
 * 2. 通信功能：
 *    - 发送文本消息
 *    - 执行宏命令
 *    - 接收设备消息
 *
 * 3. 服务控制：
 *    - 启动UART服务
 *    - 管理服务生命周期
 *    - 处理连接断开
 *
 * 4. 日志功能：
 *    - 记录通信日志
 *    - 支持日志查看
 *
 * 5. 状态管理：
 *    - 使用StateFlow管理UI状态
 *    - 支持屏幕显示状态
 *    - 支持服务运行状态
 */

package no.nordicsemi.android.uart.repository

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import no.nordicsemi.android.common.logger.LoggerLauncher
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionState
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionStateWithStatus
import no.nordicsemi.android.log.LogSession
import no.nordicsemi.android.log.timber.nRFLoggerTree
import no.nordicsemi.android.service.DisconnectAndStopEvent
import no.nordicsemi.android.service.ServiceManager
import no.nordicsemi.android.toolbox.scanner.SelectedDevice
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.data.ConfigurationDataSource
import no.nordicsemi.android.uart.data.MacroEol
import no.nordicsemi.android.uart.data.UARTMacro
import no.nordicsemi.android.uart.data.UARTRecord
import no.nordicsemi.android.uart.data.UARTRecordType
import no.nordicsemi.android.uart.data.UARTServiceData
import no.nordicsemi.android.uart.data.parseWithNewLineChar
import no.nordicsemi.android.utils.simpleSharedFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UARTRepository @Inject internal constructor(
    @ApplicationContext private val context: Context,
    private val serviceManager: ServiceManager,
    private val configurationDataSource: ConfigurationDataSource,
) {
    private var tree: nRFLoggerTree? = null
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    private val _data = MutableStateFlow(UARTServiceData())
    internal val data = _data.asStateFlow()

    private val _stopEvent = simpleSharedFlow<DisconnectAndStopEvent>()
    internal val stopEvent = _stopEvent.asSharedFlow()

    private val _command = simpleSharedFlow<String>()
    internal val command = _command.asSharedFlow()

    // 性能监控相关变量
    private var lastUpdateTime = System.currentTimeMillis()
    private var updateCounter = 0
    private var lastQueueWarningTime = 0L
    private val QUEUE_WARNING_INTERVAL = 5000L  // 5秒钟报警一次
    private val QUEUE_SIZE_THRESHOLD = 1000     // 队列大小警告阈值

    val isRunning = data.map { it.connectionState?.state == GattConnectionState.STATE_CONNECTED }

    val lastConfigurationName = configurationDataSource.lastConfigurationName

    private var isOnScreen = false
    private var isServiceRunning = false

    // 监控状态更新频率
    private fun logStateUpdate() {
        updateCounter++
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastUpdateTime >= 1000) {
            val duration = (currentTime - lastUpdateTime) / 1000.0
            val frequency = updateCounter / duration
//            android.util.Log.d("REPO_STATS", """状态更新统计:
//                |更新频率: $frequency 次/秒
//                |消息数量: ${_data.value.messages.size}
//                |内存使用: ${Runtime.getRuntime().totalMemory() / 1024 / 1024}MB
//            """.trimMargin())
            updateCounter = 0
            lastUpdateTime = currentTime
        }
    }

    // 监控数据队列大小
    private fun monitorQueueSize() {
        val queueSize = _data.value.messages.size
        val currentTime = System.currentTimeMillis()
        if (queueSize > QUEUE_SIZE_THRESHOLD && currentTime - lastQueueWarningTime > QUEUE_WARNING_INTERVAL) {
//            android.util.Log.w("REPO_MONITOR", """数据队列警告:
//                |队列大小: $queueSize
//                |可能存在数据积压
//                |内存使用: ${Runtime.getRuntime().totalMemory() / 1024 / 1024}MB
//            """.trimMargin())
            lastQueueWarningTime = currentTime
        }
    }

    fun setOnScreen(isOnScreen: Boolean) {
        this.isOnScreen = isOnScreen
        if (shouldClean()) clean()
    }

    fun setServiceRunning(serviceRunning: Boolean) {
        this.isServiceRunning = serviceRunning
        if (shouldClean()) clean()
    }

    private fun shouldClean() = !isOnScreen && !isServiceRunning

    fun launch(device: SelectedDevice) {
        tree = nRFLoggerTree(context, "UART", device.device.address, device.name)
            .apply { setLoggingTagsEnabled(false) }
            .also { Timber.plant(it) }

        _data.value = _data.value.copy(deviceName = device.name)
        serviceManager.startService(UARTService::class.java, device.device, context.getString(R.string.uart_title))
    }

    fun onConnectionStateChanged(connectionState: GattConnectionStateWithStatus?) {
        val startTime = System.nanoTime()
        _data.value = _data.value.copy(connectionState = connectionState)
        val processTime = (System.nanoTime() - startTime) / 1000000.0
//        android.util.Log.d("REPO_PERFORMANCE", "连接状态更新耗时: ${processTime}ms")
        logStateUpdate()
    }

    fun onBatteryLevelChanged(batteryLevel: Int) {
        val startTime = System.nanoTime()
        _data.value = _data.value.copy(batteryLevel = batteryLevel)
        val processTime = (System.nanoTime() - startTime) / 1000000.0
//        android.util.Log.d("REPO_PERFORMANCE", "电池电量更新耗时: ${processTime}ms")
        logStateUpdate()
    }

    fun onNewMessageReceived(value: String) {
        val startTime = System.nanoTime()
        scope.launch {
            try {
                _data.value.receiveData(value)
                _data.value = _data.value.copy(messages = _data.value.messages + UARTRecord(value, UARTRecordType.OUTPUT))
                val processTime = (System.nanoTime() - startTime) / 1000000.0
//                android.util.Log.d("REPO_PERFORMANCE", """数据接收性能:
//                    |处理时间: ${processTime}ms
//                    |数据长度: ${value.length}
//                    |消息队列大小: ${_data.value.messages.size}
//                """.trimMargin())
                logStateUpdate()
                monitorQueueSize()
            } catch (e: Exception) {
//                android.util.Log.e("REPO_ERROR", "数据处理错误", e)
            }
        }
    }

    fun onNewMessageSent(value: String) {
        val startTime = System.nanoTime()
        _data.value = _data.value.copy(messages = _data.value.messages + UARTRecord(value, UARTRecordType.INPUT))
        val processTime = (System.nanoTime() - startTime) / 1000000.0
//        android.util.Log.d("REPO_PERFORMANCE", """数据发送性能:
//            |处理时间: ${processTime}ms
//            |数据长度: ${value.length}
//            |消息队列大小: ${_data.value.messages.size}
//        """.trimMargin())
        logStateUpdate()
        monitorQueueSize()
    }

    fun sendText(text: String, newLineChar: MacroEol) {
        if (text == "on" || text == "off") {
            val commandText = when (newLineChar) {
                MacroEol.LF -> "$text\n"
                MacroEol.CR -> "$text\r"
                MacroEol.CR_LF -> "$text\r\n"
            }
            _command.tryEmit(commandText)
        } else {
            _command.tryEmit(text.parseWithNewLineChar(newLineChar))
        }
    }

    fun runMacro(macro: UARTMacro) {
        if (macro.command == null) {
            return
        }
        _command.tryEmit(macro.command.parseWithNewLineChar(macro.newLineChar))
    }

    fun clearItems() {
        _data.value = _data.value.copy(messages = emptyList())
    }

    fun openLogger() {
        LoggerLauncher.launch(context, tree?.session as? LogSession)
    }

    fun onMissingServices() {
        _data.value = _data.value.copy(missingServices = true)
        _stopEvent.tryEmit(DisconnectAndStopEvent())
    }

    suspend fun saveConfigurationName(name: String) {
        configurationDataSource.saveConfigurationName(name)
    }

    fun disconnect() {
        _stopEvent.tryEmit(DisconnectAndStopEvent())
    }

    private fun clean() {
        tree?.let { Timber.uproot(it) }
        tree = null
        _data.value = UARTServiceData()
    }
    //修改
    fun switchZoomIn() {
        _data.value = _data.value.copy(zoomIn = !_data.value.zoomIn)
    }


}
